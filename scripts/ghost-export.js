#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const { marked } = require('marked');
const { program } = require('commander');
const crypto = require('crypto');

// Configure marked for better HTML output
marked.setOptions({
  gfm: true,
  breaks: false,
  sanitize: false,
  smartypants: true
});

// Command line argument parsing
program
  .name('ghost-export')
  .description('Export Hugo posts or specific pages to Ghost JSON format')
  .option('--from <date>', 'Start date (YYYY-MM-DD)')
  .option('--to <date>', 'End date (YYYY-MM-DD)')
  .option('--output <file>', 'Output file path', 'ghost-export.json')
  .option('--posts-dir <dir>', 'Posts directory', 'content/posts')
  .option('--author <name>', 'Default author name', '<PERSON>')
  .option('--author-email <email>', 'Default author email', '<EMAIL>')
  .option('--page <name>', 'Generate specific page (github-sponsors)')
  .option('--dry-run', 'Show what would be exported without creating file')
  .option('--verbose', 'Show detailed output')
  .addHelpText('after', `
Examples:
  Export all posts:
    $ node scripts/ghost-export.js

  Export posts from a date range:
    $ node scripts/ghost-export.js --from 2024-01-01 --to 2024-12-31

  Export GitHub sponsors page:
    $ node scripts/ghost-export.js --page github-sponsors --output sponsors.json

  Dry run to see what would be exported:
    $ node scripts/ghost-export.js --page github-sponsors --dry-run
`)
  .parse();

const options = program.opts();

// Utility functions
const generateId = () => crypto.randomBytes(12).toString('hex');

const parseDate = (dateStr) => {
  if (!dateStr) return null;
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
};

const formatGhostDate = (date) => {
  return date.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');
};

const slugify = (text) => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
};

// Parse CSV file
const parseCSV = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',');

    return lines.slice(1).map(line => {
      const values = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current);
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current);

      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });
  } catch (error) {
    console.error(`Error parsing CSV ${filePath}:`, error.message);
    return [];
  }
};

// Generate GitHub profile URL
const githubProfileUrl = (handle) => {
  return `https://github.com/${handle}`;
};

// Check if sponsor is active
const isActiveSponsor = (lastTransactionDate) => {
  if (!lastTransactionDate) return false;

  const transactionDate = new Date(lastTransactionDate);
  const currentDate = new Date();

  // Consider active if last transaction was in the current or previous month
  const transactionMonth = transactionDate.getFullYear() * 12 + transactionDate.getMonth();
  const currentMonth = currentDate.getFullYear() * 12 + currentDate.getMonth();

  return (currentMonth - transactionMonth) <= 1;
};

// Generate GitHub Sponsors page HTML
const generateGitHubSponsorsPage = () => {
  const csvPath = path.join(__dirname, '..', 'data', 'github-sponsors.csv');

  if (!fs.existsSync(csvPath)) {
    console.error(`❌ GitHub sponsors CSV file not found: ${csvPath}`);
    process.exit(1);
  }

  console.log('📊 Reading GitHub sponsors data...');
  const sponsorsData = parseCSV(csvPath);

  // Process sponsors data
  const currentSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'true')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  const pastSponsors = sponsorsData
    .filter(sponsor => sponsor.is_active === 'false')
    .sort((a, b) => new Date(a.sponsorship_started_on) - new Date(b.sponsorship_started_on))
    .reverse();

  // Generate HTML content
  let html = '';

  // Page wrapper for scoping styles
  html += '<div class="github-sponsors-page">\n';

  // Current sponsors section (responsive 2-column grid)
  html += '<section class="kg-card kg-width-wide sponsors-section sponsors-current" aria-labelledby="current-sponsors-heading">\n';
  html += '  <h2 id="current-sponsors-heading">Current Sponsors</h2>\n';
  html += '  <p>I\'m incredibly grateful for the support from my current GitHub Sponsors. Your contributions help me dedicate more time to open source work and make it sustainable. Thank you! 🙏</p>\n';

  if (currentSponsors.length > 0) {
    html += '  <div class="sponsor-grid current-sponsors-grid">\n';
    currentSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      html += '    <article class="sponsor-card">\n';
      html += `      <a href="${profileUrl}" target="_blank" rel="noopener noreferrer" class="sponsor-link">\n`;
      if (sponsor.avatar_url) {
        html += `        <img src="${sponsor.avatar_url}" alt="${name}" width="96" height="96" class="sponsor-avatar">\n`;
      }
      html += '        <div class="sponsor-info">\n';
      html += `          <h3 class="sponsor-name">${name}</h3>\n`;
      html += '        </div>\n';
      html += '      </a>\n';
      html += '    </article>\n';
    });
    html += '  </div>\n';
  } else {
    html += '  <p><em>No current sponsors at the moment.</em></p>\n';
  }
  html += '</section>\n\n';

  // Past sponsors section (compact single-column list)
  html += '<section class="kg-card sponsors-section sponsors-past" aria-labelledby="past-sponsors-heading">\n';
  html += '  <h2 id="past-sponsors-heading">Past Sponsors</h2>\n';
  html += '  <p>Thank you to all my past sponsors for their support! 🙏</p>\n';

  if (pastSponsors.length > 0) {
    html += '  <ul class="sponsor-list past-sponsors-list">\n';
    pastSponsors.forEach(sponsor => {
      const profileUrl = githubProfileUrl(sponsor.sponsor_handle);
      const name = sponsor.sponsor_name || sponsor.sponsor_handle;
      const handle = sponsor.sponsor_handle;
      html += '    <li class="sponsor-list-item">\n';
      if (sponsor.avatar_url) {
        html += `      <img src="${sponsor.avatar_url}" alt="${name}" width="36" height="36" class="sponsor-avatar">\n`;
      }
      html += '      <div class="sponsor-info">\n';
      html += `        <a href="${profileUrl}" target="_blank" rel="noopener noreferrer" class="sponsor-handle">@${handle}</a>\n`;
      html += '      </div>\n';
      html += '    </li>\n';
    });
    html += '  </ul>\n';
  } else {
    html += '  <p><em>No past sponsors.</em></p>\n';
  }
  html += '</section>\n';
  // Close wrapper
  html += '</div>\n';

  return {
    title: 'GitHub Sponsors',
    slug: 'github-sponsors',
    html: html,
    currentSponsors: currentSponsors.length,
    pastSponsors: pastSponsors.length
  };
};

// Parse Hugo post file
const parseHugoPost = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Extract YAML front matter
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      console.warn(`No front matter found in ${filePath}`);
      return null;
    }

    const frontMatter = yaml.load(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();

    // Parse date
    const postDate = parseDate(frontMatter.date);
    if (!postDate) {
      console.warn(`Invalid date in ${filePath}: ${frontMatter.date}`);
      return null;
    }

    // Filter by date range if specified
    if (options.from) {
      const fromDate = parseDate(options.from);
      if (fromDate && postDate < fromDate) return null;
    }

    if (options.to) {
      const toDate = parseDate(options.to);
      if (toDate && postDate > toDate) return null;
    }

    // Convert markdown to HTML
    const htmlContent = marked(markdownContent);

    // Extract slug from front matter or generate from title
    const slug = frontMatter.slug || slugify(frontMatter.title);

    return {
      frontMatter,
      markdownContent,
      htmlContent,
      postDate,
      slug,
      filePath
    };
  } catch (error) {
    console.error(`Error parsing ${filePath}:`, error.message);
    return null;
  }
};

// Find all Hugo posts
const findHugoPosts = (postsDir) => {
  const posts = [];

  try {
    const entries = fs.readdirSync(postsDir, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.name === '_index.md') continue;

      const fullPath = path.join(postsDir, entry.name);

      if (entry.isDirectory()) {
        // Check for index.md in subdirectory
        const indexPath = path.join(fullPath, 'index.md');
        if (fs.existsSync(indexPath)) {
          const post = parseHugoPost(indexPath);
          if (post) posts.push(post);
        }
      } else if (entry.name.endsWith('.md')) {
        // Direct markdown file
        const post = parseHugoPost(fullPath);
        if (post) posts.push(post);
      }
    }
  } catch (error) {
    console.error(`Error reading posts directory ${postsDir}:`, error.message);
    process.exit(1);
  }

  return posts.sort((a, b) => a.postDate - b.postDate);
};

// Create Ghost post object
const createGhostPost = (hugoPost, postId) => {
  const { frontMatter, htmlContent, postDate, slug } = hugoPost;

  return {
    id: postId,
    uuid: crypto.randomUUID(),
    title: frontMatter.title,
    slug: slug,
    html: htmlContent,
    comment_id: postId,
    plaintext: htmlContent.replace(/<[^>]*>/g, ''), // Strip HTML for plaintext
    feature_image: frontMatter.feature_image || frontMatter.image || null,
    featured: frontMatter.featured || false,
    type: frontMatter.type || 'post',
    status: frontMatter.draft ? 'draft' : 'published',
    visibility: frontMatter.visibility || 'public',
    created_at: formatGhostDate(postDate),
    updated_at: formatGhostDate(postDate),
    published_at: frontMatter.draft ? null : formatGhostDate(postDate),
    custom_excerpt: frontMatter.excerpt || frontMatter.description || null
  };
};

// Create Ghost page object
const createGhostPage = (pageData, pageId) => {
  const currentDate = new Date();

  return {
    id: pageId,
    uuid: crypto.randomUUID(),
    title: pageData.title,
    slug: pageData.slug,
    html: pageData.html,
    comment_id: pageId,
    plaintext: pageData.html.replace(/<[^>]*>/g, ''), // Strip HTML for plaintext
    feature_image: null,
    featured: false,
    type: 'page',
    status: 'published',
    visibility: 'public',
    created_at: formatGhostDate(currentDate),
    updated_at: formatGhostDate(currentDate),
    published_at: formatGhostDate(currentDate),
    custom_excerpt: null
  };
};

// Create Ghost tag objects
const createGhostTags = (allTags) => {
  return Array.from(allTags).map(tagName => ({
    id: generateId(),
    name: tagName,
    slug: slugify(tagName),
    description: null,
    visibility: 'public',
    created_at: formatGhostDate(new Date()),
    updated_at: formatGhostDate(new Date())
  }));
};

// Create Ghost user (author) object
const createGhostUser = (authorId) => {
  return {
    id: authorId,
    name: options.author,
    slug: slugify(options.author),
    email: options.authorEmail,
    profile_image: null,
    cover_image: null,
    bio: null,
    website: null,
    location: null,
    facebook: null,
    twitter: null,
    accessibility: null,
    status: 'active',
    meta_title: null,
    meta_description: null,
    tour: null,
    last_seen: formatGhostDate(new Date()),
    created_at: formatGhostDate(new Date()),
    updated_at: formatGhostDate(new Date()),
    roles: ['Author']
  };
};

// Export specific page to Ghost
const exportPageToGhost = (pageName) => {
  if (pageName === 'github-sponsors') {
    console.log('📊 Generating GitHub Sponsors page...');
    const pageData = generateGitHubSponsorsPage();

    // Generate IDs
    const authorId = generateId();
    const pageId = generateId();

    // Create Ghost objects
    const ghostPage = createGhostPage(pageData, pageId);
    const ghostUsers = [createGhostUser(authorId)];

    // Create relationships
    const postsAuthors = [{
      id: generateId(),
      post_id: pageId,
      author_id: authorId,
      sort_order: 0
    }];

    // Create Ghost JSON structure
    const ghostData = {
      meta: {
        exported_on: Date.now(),
        version: "6.0.0"
      },
      data: {
        posts: [ghostPage],
        tags: [],
        posts_tags: [],
        users: ghostUsers,
        posts_authors: postsAuthors
      }
    };

    // Show summary
    console.log(`✅ GitHub Sponsors page generated successfully!`);
    console.log(`👥 Current sponsors: ${pageData.currentSponsors}`);
    console.log(`👥 Past sponsors: ${pageData.pastSponsors}`);

    if (options.dryRun) {
      console.log('\n🔍 Dry run completed - no file was created');
      console.log('Run without --dry-run to create the export file');
      return;
    }

    // Write to file
    try {
      fs.writeFileSync(options.output, JSON.stringify(ghostData, null, 2));
      console.log(`💾 Output saved to: ${options.output}`);
    } catch (error) {
      console.error('❌ Error writing output file:', error.message);
      process.exit(1);
    }
  } else {
    console.error(`❌ Unknown page: ${pageName}`);
    console.error('Available pages: github-sponsors');
    process.exit(1);
  }
};

// Main export function
const exportToGhost = () => {
  // Check if we're exporting a specific page
  if (options.page) {
    exportPageToGhost(options.page);
    return;
  }

  console.log('🔍 Finding Hugo posts...');
  const hugoPosts = findHugoPosts(options.postsDir);

  if (hugoPosts.length === 0) {
    console.log('❌ No posts found matching criteria');
    process.exit(0);
  }

  console.log(`📝 Found ${hugoPosts.length} posts to export`);

  // Collect all unique tags
  const allTags = new Set();
  hugoPosts.forEach(post => {
    if (post.frontMatter.tags) {
      post.frontMatter.tags.forEach(tag => allTags.add(tag));
    }
    if (post.frontMatter.categories) {
      post.frontMatter.categories.forEach(category => allTags.add(category));
    }
  });

  // Generate IDs
  const authorId = generateId();
  const postIds = hugoPosts.map(() => generateId());

  // Create Ghost objects
  const ghostPosts = hugoPosts.map((post, index) => createGhostPost(post, postIds[index]));
  const ghostTags = createGhostTags(allTags);
  const ghostUsers = [createGhostUser(authorId)];

  // Create tag mappings
  const tagMap = new Map();
  ghostTags.forEach(tag => tagMap.set(tag.name, tag.id));

  // Create relationships
  const postsAuthors = ghostPosts.map(post => ({
    id: generateId(),
    post_id: post.id,
    author_id: authorId,
    sort_order: 0
  }));

  const postsTags = [];
  hugoPosts.forEach((hugoPost, index) => {
    const postId = postIds[index];
    let sortOrder = 0;

    // Add tags
    if (hugoPost.frontMatter.tags) {
      hugoPost.frontMatter.tags.forEach(tagName => {
        const tagId = tagMap.get(tagName);
        if (tagId) {
          postsTags.push({
            id: generateId(),
            post_id: postId,
            tag_id: tagId,
            sort_order: sortOrder++
          });
        }
      });
    }

    // Add categories as tags
    if (hugoPost.frontMatter.categories) {
      hugoPost.frontMatter.categories.forEach(categoryName => {
        const tagId = tagMap.get(categoryName);
        if (tagId) {
          postsTags.push({
            id: generateId(),
            post_id: postId,
            tag_id: tagId,
            sort_order: sortOrder++
          });
        }
      });
    }
  });

  // Create Ghost JSON structure
  const ghostData = {
    meta: {
      exported_on: Date.now(),
      version: "6.0.0"
    },
    data: {
      posts: ghostPosts,
      tags: ghostTags,
      posts_tags: postsTags,
      users: ghostUsers,
      posts_authors: postsAuthors
    }
  };

  // Show summary
  console.log(`✅ Export completed successfully!`);
  console.log(`📄 Exported ${ghostPosts.length} posts`);
  console.log(`🏷️  Created ${ghostTags.length} tags`);

  if (options.verbose) {
    console.log('\n📋 Posts exported:');
    hugoPosts.forEach((post) => {
      console.log(`  - ${post.frontMatter.title} (${post.postDate.toISOString().split('T')[0]})`);
    });

    console.log('\n🏷️  Tags created:');
    ghostTags.forEach(tag => {
      console.log(`  - ${tag.name}`);
    });
  }

  if (options.dryRun) {
    console.log('\n🔍 Dry run completed - no file was created');
    console.log('Run without --dry-run to create the export file');
    return;
  }

  // Write to file
  try {
    fs.writeFileSync(options.output, JSON.stringify(ghostData, null, 2));
    console.log(`💾 Output saved to: ${options.output}`);
  } catch (error) {
    console.error('❌ Error writing output file:', error.message);
    process.exit(1);
  }
};

// Check dependencies
const checkDependencies = () => {
  const requiredPackages = ['js-yaml', 'marked', 'commander'];
  const missing = [];

  for (const pkg of requiredPackages) {
    try {
      require.resolve(pkg);
    } catch (error) {
      missing.push(pkg);
    }
  }

  if (missing.length > 0) {
    console.error('❌ Missing required packages. Please install:');
    console.error(`npm install ${missing.join(' ')}`);
    process.exit(1);
  }
};

// Run the export
if (require.main === module) {
  checkDependencies();
  exportToGhost();
}

module.exports = { exportToGhost, parseHugoPost, createGhostPost };
