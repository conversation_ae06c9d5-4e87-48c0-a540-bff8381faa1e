/* GitHub Sponsors page custom styles for Ghost.io */
/* Inject this CSS into Ghost.io admin under Code Injection > Site Header */

/* Section spacing */
.sponsors-section {
  margin: 2rem 0;
}
.sponsors-section h2 {
  margin: 0 0 .75rem;
  font-size: clamp(1.25rem, 2vw, 1.6rem);
}
.sponsors-section p {
  margin: 0 0 1rem;
}

/* Current sponsors: responsive 2-column grid */
.sponsor-grid.current-sponsors-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  align-items: stretch;
}

@media (min-width: 600px) {
  .sponsor-grid.current-sponsors-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.sponsor-card {
  border: 1px solid var(--color-border, rgba(0,0,0,0.08));
  border-radius: 12px;
  background: var(--color-bg, #fff);
  box-shadow: 0 .5px 1px rgba(0,0,0,0.06);
  transition: transform .12s ease, box-shadow .12s ease;
  overflow: hidden;
}

.sponsor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 14px rgba(0,0,0,0.12);
}

.sponsor-card .sponsor-link {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  text-decoration: none;
  color: inherit;
}

/* Large avatar for current sponsors */
.sponsor-card .sponsor-avatar {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

/* Name emphasis */
.sponsor-card .sponsor-name {
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.35;
  font-weight: 700;
}

/* Past sponsors: compact list */
.sponsor-list.past-sponsors-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sponsor-list.past-sponsors-list .sponsor-list-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 0;
  border-bottom: 1px solid var(--color-border, rgba(0,0,0,0.06));
}

/* Small avatar for past sponsors */
.sponsor-list.past-sponsors-list .sponsor-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

/* Handle link */
.sponsor-handle {
  color: inherit;
  text-decoration: none;
  font-weight: 600;
}
.sponsor-handle:hover {
  text-decoration: underline;
}

/* Ghost card spacing adjustments */
.kg-card.sponsors-section {
  padding: 0;
  box-shadow: none;
  border: 0;
  background: transparent;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sponsor-card {
    border-color: rgba(255,255,255,0.12);
    box-shadow: 0 .5px 1px rgba(0,0,0,0.3);
  }
  .sponsor-list.past-sponsors-list .sponsor-list-item {
    border-bottom-color: rgba(255,255,255,0.12);
  }
}

