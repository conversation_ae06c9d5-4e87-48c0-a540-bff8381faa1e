/* GitHub Sponsors page custom styles for Ghost.io */
/* Scope to these classes that only appear on the sponsors page content */

/* Section spacing */
.sponsors-section { margin: 2rem 0; }
.sponsors-section h2 { margin: 0 0 .75rem; font-size: clamp(1.25rem, 2vw, 1.6rem); }
.sponsors-section p { margin: 0 0 1rem; }

/* Current sponsors: responsive 2-column cards */
.current-sponsors .sponsor-grid,
.sponsors-current .sponsor-grid { display: grid; grid-template-columns: 1fr; gap: 16px; align-items: stretch; }
@media (min-width: 600px) {
  .current-sponsors .sponsor-grid,
  .sponsors-current .sponsor-grid { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

.sponsor-card { 
  border: 1px solid var(--color-border, rgba(0,0,0,0.08));
  border-radius: 12px; 
  background: var(--ghost-accent-color, var(--color-bg, #fff));
  background: var(--color-bg, #fff);
  box-shadow: 0 .5px 1px rgba(0,0,0,0.06);
  transition: transform .12s ease, box-shadow .12s ease;
}
.sponsor-card:hover { transform: translateY(-1px); box-shadow: 0 4px 14px rgba(0,0,0,0.08); }

.sponsor-card .sponsor-link { 
  display: flex; align-items: center; gap: 16px; 
  padding: 16px; text-decoration: none; color: inherit;
}

/* Large avatar for current sponsors */
.sponsor-card .sponsor-avatar { 
  width: 96px; height: 96px; border-radius: 50%; object-fit: cover; flex-shrink: 0;
}

/* Name emphasis */
.sponsor-card .sponsor-name { margin: 0; font-size: 1.125rem; line-height: 1.35; font-weight: 700; }

/* Past sponsors: compact list */
.past-sponsors-list,
.sponsors-past .sponsor-list { list-style: none; margin: 0; padding: 0; }

.past-sponsors-list .sponsor-list-item,
.sponsors-past .sponsor-list-item { 
  display: flex; align-items: center; gap: 10px; 
  padding: 6px 0; border-bottom: 1px solid var(--color-border, rgba(0,0,0,0.06));
}

/* Small avatar for past sponsors */
.past-sponsors-list .sponsor-avatar,
.sponsors-past .sponsor-avatar { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; }

/* Handle link */
.sponsor-handle { color: inherit; text-decoration: none; font-weight: 600; }
.sponsor-handle:hover { text-decoration: underline; }

/* Tweak Ghost card spacing when used inside editor cards */
.kg-card.sponsors-section { padding: 0; box-shadow: none; border: 0; background: transparent; }

/* Dark mode niceties */
@media (prefers-color-scheme: dark) {
  .sponsor-card { border-color: rgba(255,255,255,0.12); box-shadow: 0 .5px 1px rgba(0,0,0,0.3); }
  .past-sponsors-list .sponsor-list-item,
  .sponsors-past .sponsor-list-item { border-bottom-color: rgba(255,255,255,0.12); }
}

