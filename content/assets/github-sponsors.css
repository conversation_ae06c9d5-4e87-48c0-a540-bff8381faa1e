/* GitHub Sponsors page custom styles for Ghost.io */
/* Scope to these classes that only appear on the sponsors page content */

/* Section spacing */
.sponsors-section { margin: 2rem 0; }
.sponsors-section h2 { margin: 0 0 .75rem; font-size: clamp(1.25rem, 2vw, 1.6rem); }
.sponsors-section p { margin: 0 0 1rem; }

/* Main sponsors grid layout */
.sponsors-grid {
  display: grid;
  gap: 16px;
  margin: 2rem 0;
}

/* Current sponsors: responsive 2-column cards */
.current-sponsors.sponsors-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

/* Past sponsors: smaller grid */
.past-sponsors.sponsors-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

@media (max-width: 600px) {
  .current-sponsors.sponsors-grid,
  .past-sponsors.sponsors-grid {
    grid-template-columns: 1fr;
  }
}

.sponsor-card {
  border: 1px solid var(--color-border, rgba(0,0,0,0.08));
  border-radius: 12px;
  background: var(--color-bg, #fff);
  box-shadow: 0 .5px 1px rgba(0,0,0,0.06);
  transition: transform .12s ease, box-shadow .12s ease;
  overflow: hidden;
}
.sponsor-card:hover { transform: translateY(-2px); box-shadow: 0 4px 14px rgba(0,0,0,0.12); }

.sponsor-card .sponsor-link {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

/* Current sponsors: horizontal layout */
.current-sponsors .sponsor-link {
  flex-direction: row;
}

/* Past sponsors: vertical layout */
.past-sponsors .sponsor-link {
  flex-direction: column;
  text-align: center;
  gap: 12px;
}

/* Avatar sizing */
.current-sponsors .sponsor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.past-sponsors .sponsor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

/* Name emphasis */
.sponsor-info h3 {
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.35;
  font-weight: 600;
  color: var(--color-primary, #000);
}

.past-sponsors .sponsor-info h3 {
  font-size: 1rem;
}

/* Past sponsors: compact list */
.past-sponsors-list,
.sponsors-past .sponsor-list { list-style: none; margin: 0; padding: 0; }

.past-sponsors-list .sponsor-list-item,
.sponsors-past .sponsor-list-item { 
  display: flex; align-items: center; gap: 10px; 
  padding: 6px 0; border-bottom: 1px solid var(--color-border, rgba(0,0,0,0.06));
}

/* Small avatar for past sponsors */
.past-sponsors-list .sponsor-avatar,
.sponsors-past .sponsor-avatar { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; }

/* Handle link */
.sponsor-handle { color: inherit; text-decoration: none; font-weight: 600; }
.sponsor-handle:hover { text-decoration: underline; }

/* Tweak Ghost card spacing when used inside editor cards */
.kg-card.sponsors-section { padding: 0; box-shadow: none; border: 0; background: transparent; }

/* Dark mode niceties */
@media (prefers-color-scheme: dark) {
  .sponsor-card { border-color: rgba(255,255,255,0.12); box-shadow: 0 .5px 1px rgba(0,0,0,0.3); }
  .past-sponsors-list .sponsor-list-item,
  .sponsors-past .sponsor-list-item { border-bottom-color: rgba(255,255,255,0.12); }
}

